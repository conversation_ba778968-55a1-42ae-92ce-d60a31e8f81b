import 'package:tubewell_water_billing/services/account_service.dart';

/// Mixin that provides centralized account filtering logic for repositories
///
/// This eliminates the manual duplication of account filtering code across
/// all repository classes by providing a single, powerful filtering method.
mixin AccountFilteringMixin {
  /// Get current account ID from AccountService
  String? get _currentAccountId => AccountService.currentAccount?.id;

  /// Build account filter with support for additional conditions and table aliases
  ///
  /// This method handles all filtering scenarios:
  /// - Simple account-only filtering
  /// - Additional WHERE conditions
  /// - Table aliases for JOIN queries
  /// - Raw SQL query fragments
  /// - Proper SQL parentheses grouping
  ///
  /// Returns a Map with 'where' and 'whereArgs' keys that can be used
  /// directly with db.query() methods or extracted for raw queries.
  ///
  /// Examples:
  /// ```dart
  /// // Simple account filtering
  /// final filter = buildFilter();
  /// final maps = await db.query('table', where: filter['where'], whereArgs: filter['whereArgs']);
  ///
  /// // With additional conditions
  /// final filter = buildFilter(
  ///   additionalConditions: ['name LIKE ?', 'status = ?'],
  ///   additionalArgs: ['%john%', 'active'],
  /// );
  ///
  /// // For JOIN queries with table alias
  /// final filter = buildFilter(
  ///   tableAlias: 'c',
  ///   additionalConditions: ['c.name LIKE ?'],
  ///   additionalArgs: ['%search%'],
  /// );
  ///
  /// // For raw SQL queries
  /// String query = 'SELECT * FROM bills b WHERE 1=1';
  /// final filter = buildFilter(tableAlias: 'b');
  /// query += ' AND ${filter['where']}';
  /// final result = await db.rawQuery(query, filter['whereArgs']);
  /// ```
  Map<String, dynamic> buildFilter({
    String? tableAlias,
    List<String>? additionalConditions,
    List<dynamic>? additionalArgs,
  }) {
    final prefix = tableAlias != null ? '$tableAlias.' : '';
    String whereClause;
    List<dynamic> whereArgs = [];

    // Build account filtering clause
    if (_currentAccountId != null) {
      whereClause = '${prefix}accountId = ? OR ${prefix}accountId IS NULL';
      whereArgs.add(_currentAccountId);
    } else {
      whereClause = '${prefix}accountId IS NULL';
    }

    // Add additional conditions if provided
    if (additionalConditions != null && additionalConditions.isNotEmpty) {
      final additionalWhere = additionalConditions.join(' AND ');
      whereClause = '($whereClause) AND ($additionalWhere)';
      if (additionalArgs != null) {
        whereArgs.addAll(additionalArgs);
      }
    }

    return {
      'where': whereClause,
      'whereArgs': whereArgs,
    };
  }
}
