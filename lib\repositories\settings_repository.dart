import 'package:sqflite/sqflite.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/repositories/account_filtering_mixin.dart';

/// Repository for settings-related database operations
///
/// This repository provides a clean, simple API for storing and retrieving
/// string key-value pairs. Type conversion and default value handling
/// should be done by the calling code, not by this data access layer.
class SettingsRepository with AccountFilteringMixin {
  final DatabaseService _dbService = DatabaseService.instance;

  /// Get a setting value by key
  Future<String?> getSetting(String key) async {
    final db = await _dbService.database;
    final filter = buildFilter(
      additionalConditions: ['key = ?'],
      additionalArgs: [key],
    );

    final maps = await db.query(
      'settings',
      where: filter['where'],
      whereArgs: filter['whereArgs'],
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return maps.first['value'] as String?;
    }
    return null;
  }

  /// Set a setting value using efficient upsert operation
  Future<void> setSetting(String key, String value) async {
    final db = await _dbService.database;

    final settingData = {
      'key': key,
      'value': value,
      'accountId': _currentAccountId,
    };

    // Use INSERT OR REPLACE for efficient upsert operation
    // This eliminates the need for a redundant SELECT query
    await db.insert(
      'settings',
      settingData,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Get all settings for the current account
  Future<Map<String, String>> getAllSettings() async {
    final db = await _dbService.database;
    final filter = buildFilter();

    final maps = await db.query(
      'settings',
      where: filter['where'],
      whereArgs: filter['whereArgs'],
    );

    final settings = <String, String>{};
    for (final map in maps) {
      settings[map['key'] as String] = map['value'] as String;
    }
    return settings;
  }

  /// Get multiple settings by keys
  Future<Map<String, String?>> getSettings(List<String> keys) async {
    if (keys.isEmpty) return {};

    final db = await _dbService.database;
    final placeholders = keys.map((_) => '?').join(',');
    final filter = buildFilter(
      additionalConditions: ['key IN ($placeholders)'],
      additionalArgs: keys,
    );

    final maps = await db.query(
      'settings',
      where: filter['where'],
      whereArgs: filter['whereArgs'],
    );

    final settings = <String, String?>{};

    // Initialize all keys with null
    for (final key in keys) {
      settings[key] = null;
    }

    // Fill in the actual values
    for (final map in maps) {
      settings[map['key'] as String] = map['value'] as String;
    }

    return settings;
  }

  /// Set multiple settings at once using efficient upsert operations
  Future<void> setSettings(Map<String, String> settings) async {
    final db = await _dbService.database;

    await db.transaction((txn) async {
      for (final entry in settings.entries) {
        final settingData = {
          'key': entry.key,
          'value': entry.value,
          'accountId': _currentAccountId,
        };

        // Use INSERT OR REPLACE for efficient upsert operation
        // This eliminates the need for redundant SELECT queries
        await txn.insert(
          'settings',
          settingData,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    });
  }

  /// Delete a setting
  Future<void> deleteSetting(String key) async {
    final db = await _dbService.database;
    final filter = buildFilter(
      additionalConditions: ['key = ?'],
      additionalArgs: [key],
    );

    await db.delete(
      'settings',
      where: filter['where'],
      whereArgs: filter['whereArgs'],
    );
  }

  /// Delete multiple settings
  Future<void> deleteSettings(List<String> keys) async {
    if (keys.isEmpty) return;

    final db = await _dbService.database;

    await db.transaction((txn) async {
      for (final key in keys) {
        final filter = buildFilter(
          additionalConditions: ['key = ?'],
          additionalArgs: [key],
        );

        await txn.delete(
          'settings',
          where: filter['where'],
          whereArgs: filter['whereArgs'],
        );
      }
    });
  }

  /// Check if a setting exists
  Future<bool> hasSetting(String key) async {
    final value = await getSetting(key);
    return value != null;
  }

  /// Clear all settings for the current account
  Future<void> clearAllSettings() async {
    final db = await _dbService.database;
    final filter = buildFilter();

    await db.delete(
      'settings',
      where: filter['where'],
      whereArgs: filter['whereArgs'],
    );
  }

  /// Get settings count
  Future<int> getSettingsCount() async {
    final db = await _dbService.database;

    final filter = buildFilter();
    final query = 'SELECT COUNT(*) as count FROM settings WHERE ${filter['where']}';

    final result = await db.rawQuery(query, filter['whereArgs']);
    return result.first['count'] as int;
  }
}
