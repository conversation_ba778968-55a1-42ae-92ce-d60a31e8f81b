import 'package:sqflite/sqflite.dart';
import 'package:tubewell_water_billing/models/expense.dart';
import 'package:tubewell_water_billing/models/enums.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/repositories/account_filtering_mixin.dart';

/// Repository for expense-related database operations
class ExpenseRepository with AccountFilteringMixin {
  final DatabaseService _dbService = DatabaseService.instance;

  /// Get an expense by ID
  Future<Expense?> getExpenseById(int id) async {
    final db = await _dbService.database;
    final filter = buildFilter(
      additionalConditions: ['id = ?'],
      additionalArgs: [id],
    );

    final maps = await db.query(
      'expenses',
      where: filter['where'],
      whereArgs: filter['whereArgs'],
    );
    if (maps.isNotEmpty) {
      return Expense.fromMap(maps.first);
    }
    return null;
  }

  /// Save an expense (insert or update)
  Future<int> saveExpense(Expense expense) async {
    final db = await _dbService.database;

    // Add the current account ID to the expense data
    final expenseData = expense.toMap();
    expenseData['accountId'] = _currentAccountId;

    if (expense.id == null) {
      // Insert new expense
      return await db.insert('expenses', expenseData);
    } else {
      // Update existing expense with account scoping
      final filter = buildFilter(
        additionalConditions: ['id = ?'],
        additionalArgs: [expense.id],
      );

      await db.update(
        'expenses',
        expenseData,
        where: filter['where'],
        whereArgs: filter['whereArgs'],
      );
      return expense.id!;
    }
  }

  /// Get all expenses for the current account
  Future<List<Expense>> getAllExpenses({
    String? searchQuery,
    ExpenseCategory? category,
    DateTime? startDate,
    DateTime? endDate,
    PaymentMethod? paymentMethod,
    int? limit,
    int? offset,
  }) async {
    final db = await _dbService.database;

    List<String> conditions = [];
    List<dynamic> args = [];

    // Search query filtering
    if (searchQuery != null && searchQuery.isNotEmpty) {
      conditions.add('(description LIKE ? OR remarks LIKE ?)');
      args.addAll(['%$searchQuery%', '%$searchQuery%']);
    }

    // Category filtering
    if (category != null) {
      conditions.add('category = ?');
      args.add(category.value);
    }

    // Date range filtering
    if (startDate != null) {
      conditions.add('date >= ?');
      args.add(startDate.toIso8601String());
    }
    if (endDate != null) {
      conditions.add('date <= ?');
      args.add(endDate.toIso8601String());
    }

    // Payment method filtering
    if (paymentMethod != null) {
      conditions.add('paymentMethod = ?');
      args.add(paymentMethod.value);
    }

    // Build the complete filter with account filtering
    final filter = buildFilter(
      additionalConditions: conditions.isNotEmpty ? conditions : null,
      additionalArgs: args,
    );

    final maps = await db.query(
      'expenses',
      where: filter['where'],
      whereArgs: filter['whereArgs'],
      orderBy: 'date DESC',
      limit: limit,
      offset: offset,
    );

    return maps.map((map) => Expense.fromMap(map)).toList();
  }

  /// Get expenses with pagination
  Future<List<Expense>> getExpensesPaginated({
    int page = 0,
    int pageSize = 20,
    String? searchQuery,
    ExpenseCategory? category,
    DateTime? startDate,
    DateTime? endDate,
    PaymentMethod? paymentMethod,
  }) async {
    final offset = page * pageSize;
    return getAllExpenses(
      searchQuery: searchQuery,
      category: category,
      startDate: startDate,
      endDate: endDate,
      paymentMethod: paymentMethod,
      limit: pageSize,
      offset: offset,
    );
  }

  /// Get expense summary with efficient single query
  Future<Map<String, num>> getExpenseSummary({
    DateTime? startDate,
    DateTime? endDate,
    ExpenseCategory? category,
    PaymentMethod? paymentMethod,
  }) async {
    final db = await _dbService.database;
    
    String query = '''
      SELECT
        COUNT(*) as totalCount,
        SUM(amount) as totalAmount,
        AVG(amount) as averageAmount
      FROM expenses
      WHERE 1=1
    ''';
    
    List<dynamic> args = [];

    // Account filtering using mixin
    final accountFilter = buildFilter();
    query += ' AND ${accountFilter['where']}';
    args.addAll(accountFilter['whereArgs']);

    // Date range filtering
    if (startDate != null) {
      query += ' AND date >= ?';
      args.add(startDate.toIso8601String());
    }
    if (endDate != null) {
      query += ' AND date <= ?';
      args.add(endDate.toIso8601String());
    }

    // Category filtering
    if (category != null) {
      query += ' AND category = ?';
      args.add(category.value);
    }

    // Payment method filtering
    if (paymentMethod != null) {
      query += ' AND paymentMethod = ?';
      args.add(paymentMethod.value);
    }

    final results = await db.rawQuery(query, args);
    
    if (results.isNotEmpty) {
      final result = results.first;
      return {
        'totalCount': result['totalCount'] ?? 0,
        'totalAmount': result['totalAmount'] ?? 0.0,
        'averageAmount': result['averageAmount'] ?? 0.0,
      };
    }
    
    return {
      'totalCount': 0,
      'totalAmount': 0.0,
      'averageAmount': 0.0,
    };
  }

  /// Get expense summary by category
  Future<List<Map<String, dynamic>>> getExpenseSummaryByCategory({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final db = await _dbService.database;

    String query = '''
      SELECT
        category,
        COUNT(*) as count,
        SUM(amount) as totalAmount,
        AVG(amount) as averageAmount
      FROM expenses
      WHERE 1=1
    ''';

    List<dynamic> args = [];

    // Account filtering using mixin
    final accountFilter = buildFilter();
    query += ' AND ${accountFilter['where']}';
    args.addAll(accountFilter['whereArgs']);

    // Date range filtering
    if (startDate != null) {
      query += ' AND date >= ?';
      args.add(startDate.toIso8601String());
    }
    if (endDate != null) {
      query += ' AND date <= ?';
      args.add(endDate.toIso8601String());
    }

    query += ' GROUP BY category ORDER BY totalAmount DESC';

    final results = await db.rawQuery(query, args);
    return results;
  }

  /// Get recent expenses
  Future<List<Expense>> getRecentExpenses({int limit = 5}) async {
    final db = await _dbService.database;
    final filter = buildFilter();

    final maps = await db.query(
      'expenses',
      where: filter['where'],
      whereArgs: filter['whereArgs'],
      orderBy: 'date DESC',
      limit: limit,
    );

    return maps.map((map) => Expense.fromMap(map)).toList();
  }

  /// Get unique categories
  Future<List<String>> getCategories() async {
    final db = await _dbService.database;
    final accountFilter = buildFilter();

    final maps = await db.rawQuery('''
      SELECT DISTINCT category FROM expenses
      WHERE ${accountFilter['where']} AND category IS NOT NULL
      ORDER BY category ASC
    ''', accountFilter['whereArgs']);

    return maps.map((map) => map['category'] as String).toList();
  }

  /// Get unique payment methods
  Future<List<String>> getPaymentMethods() async {
    final db = await _dbService.database;
    final accountFilter = buildFilter();

    final maps = await db.rawQuery('''
      SELECT DISTINCT paymentMethod FROM expenses
      WHERE ${accountFilter['where']} AND paymentMethod IS NOT NULL
      ORDER BY paymentMethod ASC
    ''', accountFilter['whereArgs']);

    return maps.map((map) => map['paymentMethod'] as String).toList();
  }

  /// Delete an expense
  Future<void> deleteExpense(int id) async {
    final db = await _dbService.database;
    final filter = buildFilter(
      additionalConditions: ['id = ?'],
      additionalArgs: [id],
    );

    await db.delete(
      'expenses',
      where: filter['where'],
      whereArgs: filter['whereArgs'],
    );
  }

  /// Get expense count for pagination
  Future<int> getExpenseCount({
    String? searchQuery,
    ExpenseCategory? category,
    DateTime? startDate,
    DateTime? endDate,
    PaymentMethod? paymentMethod,
  }) async {
    final db = await _dbService.database;

    List<String> conditions = [];
    List<dynamic> args = [];

    // Search query filtering
    if (searchQuery != null && searchQuery.isNotEmpty) {
      conditions.add('(description LIKE ? OR remarks LIKE ?)');
      args.addAll(['%$searchQuery%', '%$searchQuery%']);
    }

    // Category filtering
    if (category != null) {
      conditions.add('category = ?');
      args.add(category.value);
    }

    // Date range filtering
    if (startDate != null) {
      conditions.add('date >= ?');
      args.add(startDate.toIso8601String());
    }
    if (endDate != null) {
      conditions.add('date <= ?');
      args.add(endDate.toIso8601String());
    }

    // Payment method filtering
    if (paymentMethod != null) {
      conditions.add('paymentMethod = ?');
      args.add(paymentMethod.value);
    }

    final filter = buildFilter(
      additionalConditions: conditions.isNotEmpty ? conditions : null,
      additionalArgs: args,
    );

    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM expenses WHERE ${filter['where']}',
      filter['whereArgs'],
    );

    return result.first['count'] as int;
  }
}
